import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ShopifyOrder } from '../purchases/interfaces/shopify.interface';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface TemplateVariables {
  customerName: string;
  activationCode: string;
  orderNumber: string;
  currency: string;
  totalPrice: string;
  email: string;
}

@Injectable()
export class EmailTemplateService {
  private readonly logger = new Logger(EmailTemplateService.name);
  private readonly templatesPath = join(__dirname, 'templates', 'activation');
  private translationsConfig: any;

  constructor() {
    try {
      const translationsPath = join(this.templatesPath, 'translations.json');
      this.translationsConfig = JSON.parse(readFileSync(translationsPath, 'utf8'));
    } catch (error) {
      this.logger.error('Failed to load translations config:', error);
      // Fallback configuration
      this.translationsConfig = {
        countryToLanguage: {
          'SE': 'sv', 'FI': 'fi', 'NO': 'no', 'DE': 'de', 'DK': 'da'
        },
        subjects: {
          'en': 'Your VR Training Activation Code - IMVI',
          'sv': 'Din VR-träning aktiveringskod - IMVI',
          'fi': 'VR-harjoittelun aktivointikoodi - IMVI',
          'no': 'Din VR-trenings aktiveringskode - IMVI',
          'de': 'Ihr VR-Training Aktivierungscode - IMVI',
          'da': 'Din VR-træning aktiveringskode - IMVI'
        },
        supportedLanguages: ['en', 'sv', 'fi', 'no', 'de', 'da'],
        defaultLanguage: 'en'
      };
    }
  }

  /**
   * Detect language based on billing address country
   */
  detectLanguageFromOrder(shopifyOrder: ShopifyOrder): string {
    const country = shopifyOrder.billing_address?.country || 
                   shopifyOrder.shipping_address?.country;
    
    if (!country) {
      this.logger.warn('No country found in order, using default language');
      return this.translationsConfig.defaultLanguage;
    }

    // Try exact match first
    let language = this.translationsConfig.countryToLanguage[country];
    
    // If no exact match, try case-insensitive match
    if (!language) {
      const countryUpper = country.toUpperCase();
      language = this.translationsConfig.countryToLanguage[countryUpper];
    }

    // If still no match, try to find by country name
    if (!language) {
      const countryName = this.getCountryName(country);
      language = this.translationsConfig.countryToLanguage[countryName];
    }

    const detectedLanguage = language || this.translationsConfig.defaultLanguage;
    
    this.logger.log(`Detected language '${detectedLanguage}' for country '${country}'`);
    return detectedLanguage;
  }

  /**
   * Get activation email template for specific language
   */
  getActivationEmailTemplate(
    language: string,
    variables: TemplateVariables
  ): EmailTemplate {
    // Ensure language is supported
    if (!this.translationsConfig.supportedLanguages.includes(language)) {
      this.logger.warn(`Language '${language}' not supported, falling back to default`);
      language = this.translationsConfig.defaultLanguage;
    }

    try {
      // Load HTML template
      const htmlPath = join(this.templatesPath, `${language}.html`);
      let htmlTemplate = readFileSync(htmlPath, 'utf8');

      // Load text template
      const textPath = join(this.templatesPath, `${language}.txt`);
      let textTemplate = readFileSync(textPath, 'utf8');

      // Replace variables in templates
      htmlTemplate = this.replaceVariables(htmlTemplate, variables);
      textTemplate = this.replaceVariables(textTemplate, variables);

      // Get subject
      const subject = this.translationsConfig.subjects[language] || 
                     this.translationsConfig.subjects[this.translationsConfig.defaultLanguage];

      return {
        subject,
        html: htmlTemplate,
        text: textTemplate
      };
    } catch (error) {
      this.logger.error(`Failed to load template for language '${language}':`, error);
      
      // Fallback to default language
      if (language !== this.translationsConfig.defaultLanguage) {
        this.logger.log(`Falling back to default language: ${this.translationsConfig.defaultLanguage}`);
        return this.getActivationEmailTemplate(this.translationsConfig.defaultLanguage, variables);
      }
      
      throw new Error(`Failed to load email template for language: ${language}`);
    }
  }

  /**
   * Replace template variables with actual values
   */
  private replaceVariables(template: string, variables: TemplateVariables): string {
    let result = template;
    
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), value || '');
    });

    return result;
  }

  /**
   * Convert country code to country name (basic implementation)
   */
  private getCountryName(countryCode: string): string {
    const countryNames: { [key: string]: string } = {
      'SE': 'Sweden',
      'FI': 'Finland', 
      'NO': 'Norway',
      'DE': 'Germany',
      'DK': 'Denmark',
      'AT': 'Austria',
      'CH': 'Switzerland',
      'US': 'United States',
      'CA': 'Canada',
      'GB': 'United Kingdom',
      'AU': 'Australia',
      'NZ': 'New Zealand'
    };

    return countryNames[countryCode.toUpperCase()] || countryCode;
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return this.translationsConfig.supportedLanguages;
  }

  /**
   * Get default language
   */
  getDefaultLanguage(): string {
    return this.translationsConfig.defaultLanguage;
  }
}
