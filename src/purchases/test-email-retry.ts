/**
 * Test script to verify email retry functionality
 * This script demonstrates how the retry logic works for email sending
 */

import { RetryUtil } from '../common/utils/retry.util';

// Mock mail service that simulates failures
class MockMailService {
  private attemptCount = 0;
  private shouldFailAttempts: number;

  constructor(shouldFailAttempts: number = 2) {
    this.shouldFailAttempts = shouldFailAttempts;
  }

  async sendMail(
    email: string,
    name: string,
    subject: string,
    text: string,
    html: string,
  ): Promise<any> {
    this.attemptCount++;
    console.log(`📧 Mail service attempt ${this.attemptCount} for ${email}`);

    if (this.attemptCount <= this.shouldFailAttempts) {
      // Simulate different types of failures
      const errors = [
        new Error('ECONNRESET: Connection reset by peer'),
        new Error('ETIMEDOUT: Request timeout'),
        new Error('Mail sending failed: Temporary service unavailable'),
      ];
      
      const error = errors[this.attemptCount - 1] || errors[0];
      console.log(`❌ Simulated failure: ${error.message}`);
      throw error;
    }

    console.log(`✅ Email sent successfully on attempt ${this.attemptCount}`);
    return { success: true, messageId: 'mock-message-id' };
  }

  reset() {
    this.attemptCount = 0;
  }
}

// Helper function to determine if an error should be retried
function isRetryableEmailError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code;

  // Network-related errors that should be retried
  const networkErrors = [
    'timeout',
    'network',
    'econnreset',
    'enotfound',
    'econnrefused',
    'etimedout',
    'socket hang up',
    'connection reset',
  ];

  // Mail service specific errors that should be retried
  const mailServiceErrors = [
    'mail sending failed',
    'temporary failure',
    'service unavailable',
    'rate limit',
    'too many requests',
  ];

  // Check error message
  const hasRetryableMessage = [...networkErrors, ...mailServiceErrors].some(
    (errorType) => errorMessage.includes(errorType),
  );

  // Check error codes
  const retryableCodes = [
    'ECONNRESET',
    'ENOTFOUND',
    'ECONNREFUSED',
    'ETIMEDOUT',
    'EPIPE',
  ];
  const hasRetryableCode = retryableCodes.includes(errorCode);

  // Check error names
  const retryableNames = ['AbortError', 'TypeError', 'NetworkError'];
  const hasRetryableName = retryableNames.includes(error.name);

  return hasRetryableMessage || hasRetryableCode || hasRetryableName;
}

// Test function that simulates the email sending with retry logic
async function testEmailRetry() {
  console.log('🧪 Testing email retry functionality...\n');

  const mockMailService = new MockMailService(2); // Fail first 2 attempts
  const testEmail = '<EMAIL>';
  const testName = 'John Doe';
  const testActivationCode = 'AB3x';

  try {
    await RetryUtil.withRetry(
      async () => {
        await mockMailService.sendMail(
          testEmail,
          testName,
          'Your VR Training Activation Code - IMVI',
          `Your activation code: ${testActivationCode}`,
          '<html><body>Your activation code: ' + testActivationCode + '</body></html>',
        );
      },
      {
        maxRetries: 3,
        baseDelay: 1000, // 1 second for testing
        maxDelay: 5000,
        retryCondition: (error) => isRetryableEmailError(error),
        onRetry: (error, attempt) => {
          console.log(`🔄 Retry attempt ${attempt}/3: ${error.message}`);
        },
      },
    );
    console.log('\n✅ Email retry test completed successfully!');
  } catch (error) {
    console.log(`\n❌ Email retry test failed after all attempts: ${error.message}`);
  }
}

// Test function for non-retryable errors
async function testNonRetryableError() {
  console.log('\n🧪 Testing non-retryable error handling...\n');

  const mockMailService = new MockMailService(0); // Don't fail
  
  try {
    await RetryUtil.withRetry(
      async () => {
        // Simulate a non-retryable error (e.g., invalid email format)
        const error = new Error('Invalid email format: not-an-email');
        error.name = 'ValidationError';
        throw error;
      },
      {
        maxRetries: 3,
        baseDelay: 1000,
        retryCondition: (error) => isRetryableEmailError(error),
        onRetry: (error, attempt) => {
          console.log(`🔄 Retry attempt ${attempt}/3: ${error.message}`);
        },
      },
    );
  } catch (error) {
    console.log(`❌ Non-retryable error handled correctly: ${error.message}`);
    console.log('✅ Non-retryable error test passed - no retries were attempted');
  }
}

// Run tests
async function runTests() {
  await testEmailRetry();
  await testNonRetryableError();
  console.log('\n🎉 All email retry tests completed!');
}

// Uncomment to run the tests
// runTests().catch(console.error);

export { testEmailRetry, testNonRetryableError, runTests };
